#!/usr/bin/env python3
"""
模型下载脚本 - 下载 BGE-M3 嵌入模型到本地
"""

import os
import sys
import shutil
from pathlib import Path
from typing import Optional

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from FlagEmbedding import BGEM3FlagModel
    import torch
    from rich.console import Console
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.panel import Panel
    from rich.table import Table
    from rich.prompt import Confirm
except ImportError as e:
    print(f"❌ 缺少必要的依赖包: {e}")
    print("请先运行: pip install -r requirements.txt")
    print("特别注意：需要安装 FlagEmbedding 库")
    sys.exit(1)

# 初始化控制台
console = Console()


class ModelDownloader:
    """BGE-M3模型下载器"""

    def __init__(self, cache_dir: Optional[str] = None):
        """
        初始化模型下载器

        Args:
            cache_dir: 模型缓存目录，如果为 None 则使用默认目录
        """
        if cache_dir is None:
            self.cache_dir = project_root / "cache" / "models"
        else:
            self.cache_dir = Path(cache_dir)

        # 确保缓存目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # 模型配置
        self.model_name = "BAAI/bge-m3"
        self.model_path = self.cache_dir / "bge-m3"

        # 旧模型路径（用于清理）
        self.old_model_paths = [
            self.cache_dir / "gte-large",
            self.cache_dir / "models--thenlper--gte-large"
        ]

    def check_system_requirements(self) -> bool:
        """检查系统要求"""
        console.print("\n🔍 检查系统要求...", style="blue")

        # 检查 Python 版本
        python_version = sys.version_info
        if python_version < (3, 8):
            console.print("❌ Python 版本需要 >= 3.8", style="red")
            return False

        console.print(
            f"✅ Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}",
            style="green",
        )

        # 检查 PyTorch
        try:
            torch_version = torch.__version__
            console.print(f"✅ PyTorch 版本: {torch_version}", style="green")

            # 检查 CUDA 可用性
            if torch.cuda.is_available():
                cuda_version = torch.version.cuda
                gpu_count = torch.cuda.device_count()
                console.print(
                    f"✅ CUDA 可用: {cuda_version}, GPU 数量: {gpu_count}",
                    style="green",
                )
            else:
                console.print("ℹ️  CUDA 不可用，将使用 CPU", style="yellow")

        except Exception as e:
            console.print(f"❌ PyTorch 检查失败: {e}", style="red")
            return False

        # 检查磁盘空间（BGE-M3 大约需要 2.3GB）
        try:
            import shutil

            free_space = shutil.disk_usage(self.cache_dir).free / (1024**3)  # GB
            if free_space < 3.0:
                console.print(
                    f"❌ 磁盘空间不足: {free_space:.1f}GB 可用，需要至少 3GB",
                    style="red",
                )
                return False
            console.print(f"✅ 磁盘空间充足: {free_space:.1f}GB 可用", style="green")
        except Exception as e:
            console.print(f"⚠️  无法检查磁盘空间: {e}", style="yellow")

        return True

    def clean_old_models(self) -> bool:
        """清理旧的gte-large模型文件"""
        try:
            console.print("\n🧹 检查并清理旧模型...", style="blue")

            cleaned = False
            for old_path in self.old_model_paths:
                if old_path.exists():
                    if Confirm.ask(f"发现旧模型 {old_path.name}，是否删除？"):
                        console.print(f"正在删除 {old_path}...", style="yellow")
                        shutil.rmtree(old_path)
                        console.print(f"✅ 已删除 {old_path.name}", style="green")
                        cleaned = True
                    else:
                        console.print(f"⏭️  跳过删除 {old_path.name}", style="yellow")

            if not cleaned:
                console.print("✅ 没有发现需要清理的旧模型", style="green")

            return True
        except Exception as e:
            console.print(f"❌ 清理旧模型失败: {e}", style="red")
            return False

    def is_model_downloaded(self) -> bool:
        """检查BGE-M3模型是否已下载"""
        # BGE-M3模型的关键文件
        required_files = [
            "config.json",
            "pytorch_model.bin",
            "tokenizer.json",
            "tokenizer_config.json",
        ]

        if not self.model_path.exists():
            return False

        # 检查是否可以成功加载模型
        try:
            model = BGEM3FlagModel(self.model_name, use_fp16=True)
            return True
        except Exception:
            return False

    def download_with_flagembedding(self) -> bool:
        """使用 FlagEmbedding 库下载 BGE-M3 模型"""
        try:
            console.print(
                f"\n📥 使用 FlagEmbedding 下载 BGE-M3 模型...", style="blue"
            )

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("下载 BGE-M3 模型...", total=None)

                # 下载并初始化模型
                model = BGEM3FlagModel(
                    self.model_name,
                    use_fp16=True,  # 使用半精度以节省内存
                )
                progress.update(task, completed=True)

            console.print("✅ BGE-M3 模型下载完成", style="green")
            return True

        except Exception as e:
            console.print(f"❌ BGE-M3 模型下载失败: {e}", style="red")

            # 提供详细的故障排除建议
            console.print("\n🔧 故障排除建议:", style="yellow")
            console.print("1. 检查网络连接和防火墙设置", style="cyan")
            console.print("2. 确保有足够的磁盘空间 (至少 3GB)", style="cyan")
            console.print("3. 尝试使用代理或更换网络环境", style="cyan")
            console.print("4. 检查 Hugging Face Hub 服务状态", style="cyan")

            if "timeout" in str(e).lower():
                console.print("5. 网络超时：尝试重新运行或使用更稳定的网络", style="cyan")
            elif "permission" in str(e).lower():
                console.print("5. 权限错误：检查目录写入权限", style="cyan")
            elif "space" in str(e).lower():
                console.print("5. 磁盘空间不足：清理磁盘空间后重试", style="cyan")

            return False

    def download_with_huggingface(self) -> bool:
        """备用方案：使用 Hugging Face Hub 下载模型"""
        try:
            console.print(f"\n📥 使用 Hugging Face Hub 下载模型...", style="blue")

            from huggingface_hub import snapshot_download

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("下载模型文件...", total=None)

                # 下载模型到指定目录
                snapshot_download(
                    repo_id=self.model_name,
                    cache_dir=str(self.cache_dir),
                    local_dir=str(self.model_path),
                    local_dir_use_symlinks=False
                )
                progress.update(task, completed=True)

            console.print("✅ Hugging Face Hub 下载完成", style="green")
            return True

        except Exception as e:
            console.print(f"❌ Hugging Face Hub 下载失败: {e}", style="red")
            return False

    def test_model(self) -> bool:
        """测试BGE-M3模型是否正常工作"""
        try:
            console.print("\n🧪 测试 BGE-M3 模型...", style="blue")

            # 加载模型
            model = BGEM3FlagModel(self.model_name, use_fp16=True)

            # 测试文本
            test_texts = [
                "这是一个测试句子。",
                "LangChain is a framework for building applications with LLMs.",
                "人工智能技术正在快速发展。",
                "BGE-M3 is a multilingual embedding model with multi-functionality.",
            ]

            # 生成密集嵌入
            output = model.encode(
                test_texts,
                batch_size=4,
                max_length=8192,
                return_dense=True,
                return_sparse=False,
                return_colbert_vecs=False
            )

            embeddings = output['dense_vecs']

            # 验证结果
            if embeddings.shape[0] == len(test_texts) and embeddings.shape[1] == 1024:
                console.print(
                    f"✅ BGE-M3 模型测试成功！嵌入维度: {embeddings.shape[1]}", style="green"
                )

                # 显示测试结果
                table = Table(title="BGE-M3 模型测试结果")
                table.add_column("文本", style="cyan")
                table.add_column("嵌入维度", style="magenta")
                table.add_column("嵌入范围", style="green")

                for i, text in enumerate(test_texts):
                    emb = embeddings[i]
                    table.add_row(
                        text[:40] + "..." if len(text) > 40 else text,
                        str(emb.shape[0]),
                        f"[{emb.min():.3f}, {emb.max():.3f}]",
                    )

                console.print(table)

                # 测试相似度计算
                similarity = embeddings[0] @ embeddings[1].T
                console.print(f"✅ 相似度计算测试: {similarity:.3f}", style="green")

                return True
            else:
                console.print("❌ 模型测试失败：嵌入结果异常", style="red")
                return False

        except Exception as e:
            console.print(f"❌ BGE-M3 模型测试失败: {e}", style="red")
            return False

    def show_model_info(self):
        """显示BGE-M3模型信息"""
        info_panel = Panel.fit(
            f"""
[bold blue]BGE-M3 模型信息[/bold blue]

[bold]模型名称:[/bold] {self.model_name}
[bold]模型类型:[/bold] 多功能文本嵌入模型
[bold]嵌入维度:[/bold] 1024
[bold]最大序列长度:[/bold] 8192
[bold]支持语言:[/bold] 100+ 语言（多语言）
[bold]模型大小:[/bold] ~2.3GB
[bold]缓存位置:[/bold] {self.model_path}

[bold green]特性:[/bold green]
• Multi-Functionality: 密集检索 + 稀疏检索 + 多向量检索
• Multi-Linguality: 支持100+种语言
• Multi-Granularity: 支持短句到长文档（8192 tokens）
• 在多个基准测试中表现优异
• 适用于RAG系统和语义搜索

[bold yellow]优势:[/bold yellow]
• 序列长度比gte-large长16倍（8192 vs 512）
• 更好的多语言支持和跨语言检索能力
• 支持混合检索策略，提升检索准确性
            """,
            title="📋 BGE-M3 模型详情",
            border_style="blue",
        )
        console.print(info_panel)

    def run(self) -> bool:
        """运行BGE-M3模型下载流程"""
        console.print(
            Panel.fit(
                "[bold blue]🤖 BGE-M3 模型下载器[/bold blue]\n"
                "正在为 Deep Risk RAG 系统下载 BGE-M3 嵌入模型...",
                title="BGE-M3 模型下载",
                border_style="blue",
            )
        )

        # 显示模型信息
        self.show_model_info()

        # 检查系统要求
        if not self.check_system_requirements():
            return False

        # 清理旧模型
        if not self.clean_old_models():
            console.print("⚠️  旧模型清理失败，但继续下载新模型", style="yellow")

        # 检查模型是否已存在
        if self.is_model_downloaded():
            console.print("\n✅ BGE-M3 模型已存在，跳过下载", style="green")
            return self.test_model()

        # 尝试下载模型
        console.print(f"\n📁 BGE-M3 模型将保存到: {self.model_path}", style="blue")

        # 优先使用 FlagEmbedding
        if self.download_with_flagembedding():
            return self.test_model()

        # 备用方案：使用 Hugging Face Hub
        console.print("\n🔄 尝试备用下载方案...", style="yellow")
        if self.download_with_huggingface():
            return self.test_model()

        console.print("\n❌ 所有下载方案都失败了", style="red")
        return False


def main():
    """主函数"""
    try:
        # 创建BGE-M3下载器
        downloader = ModelDownloader()

        # 运行下载
        success = downloader.run()

        if success:
            console.print("\n🎉 BGE-M3 模型下载和测试完成！", style="bold green")
            console.print("现在可以运行主程序: python main.py", style="blue")
            console.print("\n📝 注意：系统已从 gte-large 升级到 BGE-M3", style="cyan")
            console.print("   • 序列长度从 512 提升到 8192", style="cyan")
            console.print("   • 支持更多语言和更好的检索性能", style="cyan")
        else:
            console.print("\n💥 BGE-M3 模型下载失败", style="bold red")
            sys.exit(1)

    except KeyboardInterrupt:
        console.print("\n\n⏹️  下载被用户中断", style="yellow")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n💥 发生未预期的错误: {e}", style="bold red")
        sys.exit(1)


if __name__ == "__main__":
    main()
