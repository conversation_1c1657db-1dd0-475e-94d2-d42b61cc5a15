# BGE-M3模型替换任务

## 任务概述
将Deep Risk RAG系统的文本嵌入模型从gte-large完全替换为BGE-M3，提升模型性能和多语言支持能力。

## 技术对比
| 特性 | gte-large | BGE-M3 |
|------|-----------|---------|
| 维度 | 1024 | 1024 |
| 序列长度 | 512 | 8192 |
| 语言支持 | 中英文 | 100+语言 |
| 功能 | 密集检索 | 密集+稀疏+多向量检索 |
| 模型大小 | ~1.5GB | ~2.3GB |

## 执行计划

### ✅ 第一阶段：模型下载脚本更新
1. 修改 `models/download_models.py`
   - 更新模型名称和路径
   - 更新模型信息和系统要求
   - 添加旧模型清理功能

### ⏳ 第二阶段：配置文件更新  
2. 修改 `config.py`
   - 更新嵌入模型配置
   - 更新序列长度限制

### ⏳ 第三阶段：嵌入服务更新
3. 修改 `src/embeddings.py`
   - 更新类名和导入
   - 适配BGE-M3 API

### ⏳ 第四阶段：相关文件更新
4. 修改 `src/utils.py`
5. 更新示例文件

### ⏳ 第五阶段：依赖和文档更新
6. 更新 `requirements.txt`
7. 更新相关文档

## 预期收益
- 支持更长文本（8192 tokens vs 512）
- 更好的多语言支持
- 多种检索模式支持
- 更高的检索准确性

## 风险控制
- 保持API接口兼容性
- 提供旧模型清理功能
- 完整的测试验证
