"""
嵌入服务 - 基于 BGE-M3 模型的文本嵌入服务
"""

from pathlib import Path
from typing import List, Optional, Dict, Any
import time
import hashlib
import pickle

import numpy as np
from FlagEmbedding import BGEM3FlagModel
from langchain_core.embeddings import Embeddings

# 使用统一的工具模块
from .utils import get_logger, load_settings, batch_process
from .device_utils import get_optimal_device, recommend_device_settings

# 获取配置和日志
settings = load_settings()
logger = get_logger(__name__, settings.LOG_LEVEL)


class BGEM3Embeddings(Embeddings):
    """基于 BGE-M3 模型的嵌入服务"""

    def __init__(
        self,
        model_name: str = None,
        cache_dir: Optional[str] = None,
        batch_size: int = None,
        max_length: int = None,
        device: str = "auto",
        enable_cache: bool = None,
        cache_ttl: int = None,
        normalize_embeddings: bool = True,
        **kwargs,
    ):
        """
        初始化嵌入服务

        Args:
            model_name: 模型名称
            cache_dir: 模型缓存目录
            batch_size: 批处理大小
            max_length: 最大序列长度
            device: 设备 (cpu, cuda, auto)
            enable_cache: 是否启用嵌入缓存
            cache_ttl: 缓存过期时间（秒）
            normalize_embeddings: 是否标准化嵌入向量
        """
        # 使用配置或默认值
        self.model_name = model_name or settings.EMBEDDING_MODEL_NAME
        self.cache_dir = Path(cache_dir) if cache_dir else settings.MODELS_CACHE_DIR
        self.batch_size = batch_size or settings.EMBEDDING_BATCH_SIZE
        self.max_length = max_length or settings.EMBEDDING_MAX_LENGTH
        self.enable_cache = (
            enable_cache if enable_cache is not None else settings.ENABLE_CACHE
        )
        self.cache_ttl = cache_ttl or settings.CACHE_TTL_SECONDS
        self.normalize_embeddings = normalize_embeddings

        # 使用统一的设备选择逻辑
        self.device = get_optimal_device(device)

        # 获取设备推荐设置
        device_recommendations = recommend_device_settings(model_size_gb=2.3)
        self.use_fp16 = device_recommendations.get("use_fp16", False)

        # 如果用户没有指定批处理大小，使用推荐值
        if batch_size is None:
            self.batch_size = device_recommendations.get("batch_size", 32)

        # 输出设备推荐警告
        for warning in device_recommendations.get("warnings", []):
            logger.warning(warning)

        # 初始化模型
        self.model = None
        self._load_model()

        # 初始化缓存
        self.embedding_cache = {}
        self.cache_dir_path = self.cache_dir / "embedding_cache"
        if self.enable_cache:
            self.cache_dir_path.mkdir(parents=True, exist_ok=True)
            self._load_cache()

    def _load_model(self):
        """加载BGE-M3嵌入模型"""
        try:
            logger.info(f"正在加载 BGE-M3 嵌入模型: {self.model_name}")
            logger.info(f"设备: {self.device}")
            logger.info(f"缓存目录: {self.cache_dir}")

            # 加载BGE-M3模型，使用推荐的设置
            self.model = BGEM3FlagModel(
                self.model_name,
                use_fp16=self.use_fp16,
                device=self.device
            )

            logger.info("✅ BGE-M3 模型加载成功")
            logger.info("模型维度: 1024")  # BGE-M3固定维度
            logger.info(f"最大序列长度: {self.max_length}")
            logger.info(f"使用半精度: {self.use_fp16}")
            logger.info(f"设备: {self.device}")

        except Exception as e:
            logger.error(f"❌ BGE-M3 模型加载失败: {e}")

            # 提供详细的故障排除建议
            error_msg = f"无法加载 BGE-M3 嵌入模型: {e}\n\n"
            error_msg += "故障排除建议:\n"
            error_msg += "1. 确保已安装 FlagEmbedding 库: pip install FlagEmbedding\n"
            error_msg += "2. 检查网络连接，模型需要从 Hugging Face 下载\n"
            error_msg += "3. 运行模型下载脚本: python models/download_models.py\n"
            error_msg += "4. 检查磁盘空间，BGE-M3 需要约 2.3GB 空间\n"

            if "CUDA" in str(e) or "cuda" in str(e):
                error_msg += "5. CUDA 相关错误：尝试设置 device='cpu' 或检查 CUDA 安装\n"

            if "memory" in str(e).lower():
                error_msg += "5. 内存不足：尝试设置 use_fp16=True 或使用更小的 batch_size\n"

            raise RuntimeError(error_msg)

    def _get_cache_key(self, text: str) -> str:
        """生成BGE-M3特定的缓存键"""
        # 使用文本内容、模型名称、序列长度和标准化设置生成唯一键
        content = f"BGE-M3:{self.model_name}:{self.max_length}:{self.normalize_embeddings}:{text}"
        return hashlib.md5(content.encode("utf-8")).hexdigest()

    def _load_cache(self):
        """加载缓存"""
        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            if cache_file.exists():
                with open(cache_file, "rb") as f:
                    cache_data = pickle.load(f)

                # 检查缓存是否过期
                current_time = time.time()
                valid_cache = {}

                for key, (embedding, timestamp) in cache_data.items():
                    if current_time - timestamp < self.cache_ttl:
                        valid_cache[key] = (embedding, timestamp)

                self.embedding_cache = valid_cache
                logger.info(f"加载嵌入缓存: {len(valid_cache)} 条记录")

        except Exception as e:
            logger.warning(f"加载缓存失败: {e}")
            self.embedding_cache = {}

    def _save_cache(self):
        """保存缓存"""
        if not self.enable_cache:
            return

        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            with open(cache_file, "wb") as f:
                pickle.dump(self.embedding_cache, f)
        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")

    def _get_cached_embedding(self, text: str) -> Optional[np.ndarray]:
        """获取缓存的嵌入"""
        if not self.enable_cache:
            return None

        cache_key = self._get_cache_key(text)
        if cache_key in self.embedding_cache:
            embedding, timestamp = self.embedding_cache[cache_key]

            # 检查是否过期
            if time.time() - timestamp < self.cache_ttl:
                return embedding
            else:
                # 删除过期缓存
                del self.embedding_cache[cache_key]

        return None

    def _cache_embedding(self, text: str, embedding: np.ndarray):
        """缓存嵌入"""
        if not self.enable_cache:
            return

        cache_key = self._get_cache_key(text)
        self.embedding_cache[cache_key] = (embedding, time.time())

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        if not text:
            return ""

        # 移除多余的空白字符
        text = " ".join(text.split())

        # 智能文本处理 - 避免简单截断
        max_chars = self.max_length * 4  # 粗略估计，4个字符约等于1个token
        
        if len(text) > max_chars:
            logger.warning(f"文本长度 {len(text)} 字符，超过限制 {max_chars}")
            
            # 检查配置中的处理策略
            strategy = getattr(settings, 'TEXT_PROCESSING_STRATEGY', 'smart_truncate')
            
            if strategy == "head_tail":
                # 策略1：头尾保留策略
                head_length = max_chars // 2
                tail_length = max_chars // 4
                
                head_text = text[:head_length]
                tail_text = text[-tail_length:]
                
                text = f"{head_text}...[省略中间内容]...{tail_text}"
                logger.info(f"使用头尾保留策略，保留前 {head_length} 和后 {tail_length} 字符")
                
            elif strategy == "smart_truncate":
                # 策略2：智能边界截断
                # 查找最后一个句号、感叹号或问号
                sentence_ends = []
                for i, char in enumerate(text[:max_chars]):
                    if char in '。！？.!?':
                        sentence_ends.append(i)
                
                if sentence_ends and sentence_ends[-1] > max_chars * 0.8:
                    # 在句子边界截断（如果截断点不会丢失太多内容）
                    cut_point = sentence_ends[-1] + 1
                    text = text[:cut_point]
                    logger.info(f"在句子边界截断，保留 {len(text)} 字符")
                else:
                    # 在词语边界截断
                    words = text[:max_chars].split()
                    if len(words) > 1:
                        text = " ".join(words[:-1])
                        logger.info(f"在词语边界截断，保留 {len(text)} 字符")
                    else:
                        text = text[:max_chars]
                        logger.info(f"直接截断到 {len(text)} 字符")
            
            else:  # sliding_window 或其他策略
                # 策略3：简单截断（向后兼容）
                text = text[:max_chars]
                logger.info(f"使用简单截断策略，保留 {len(text)} 字符")

        return text

    def _split_long_text(self, text: str, max_chars: int, overlap: int = 50) -> List[str]:
        """将长文本分割为多个片段，用于生成多个嵌入"""
        if len(text) <= max_chars:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + max_chars
            
            if end >= len(text):
                # 最后一个片段
                chunks.append(text[start:])
                break
            
            # 尝试在句子边界分割
            chunk_text = text[start:end]
            sentence_ends = []
            for i, char in enumerate(chunk_text):
                if char in '。！？.!?':
                    sentence_ends.append(start + i)
            
            if sentence_ends and sentence_ends[-1] > start + max_chars * 0.7:
                # 在句子边界分割
                actual_end = sentence_ends[-1] + 1
                chunks.append(text[start:actual_end])
                start = actual_end - overlap
            else:
                # 在词语边界分割
                words = chunk_text.split()
                if len(words) > 1:
                    chunk_words = words[:-1]
                    chunk_text = " ".join(chunk_words)
                    chunks.append(chunk_text)
                    start = start + len(chunk_text) - overlap
                else:
                    # 强制分割
                    chunks.append(chunk_text)
                    start = end - overlap
            
            # 确保 start 向前移动，避免无限循环
            if start <= len(chunks[-1]) + (0 if len(chunks) == 1 else len(chunks[-2])):
                start = len(chunks[-1]) + (0 if len(chunks) == 1 else len(chunks[-2]))
        
        return chunks

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        嵌入文档列表

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表
        """
        if not texts:
            return []

        if self.model is None:
            raise RuntimeError("模型未加载")

        # 预处理文本
        processed_texts = [self._preprocess_text(text) for text in texts]

        # 检查缓存
        embeddings = []
        texts_to_embed = []
        indices_to_embed = []

        for i, text in enumerate(processed_texts):
            cached_embedding = self._get_cached_embedding(text)
            if cached_embedding is not None:
                embeddings.append(cached_embedding)
            else:
                embeddings.append(None)  # 占位符
                texts_to_embed.append(text)
                indices_to_embed.append(i)

        # 优化：批量嵌入未缓存的文本
        if texts_to_embed:
            cache_hit_rate = (len(texts) - len(texts_to_embed)) / len(texts) * 100
            logger.info(
                f"嵌入 {len(texts_to_embed)} 个文本（缓存命中率: {cache_hit_rate:.1f}%）"
            )

            try:
                # 优化：使用 batch_process 工具函数
                from .utils import batch_process

                new_embeddings = []

                for batch_texts in batch_process(texts_to_embed, self.batch_size):
                    # 使用BGE-M3生成密集嵌入
                    batch_output = self.model.encode(
                        batch_texts,
                        batch_size=len(batch_texts),
                        max_length=self.max_length,
                        return_dense=True,
                        return_sparse=False,
                        return_colbert_vecs=False
                    )

                    # 获取密集嵌入向量
                    batch_embeddings = batch_output['dense_vecs']

                    # 标准化嵌入（如果需要）
                    if self.normalize_embeddings:
                        import numpy as np
                        norms = np.linalg.norm(batch_embeddings, axis=1, keepdims=True)
                        batch_embeddings = batch_embeddings / norms

                    new_embeddings.extend(batch_embeddings)

                # 更新结果和缓存
                for i, embedding in enumerate(new_embeddings):
                    original_index = indices_to_embed[i]
                    embeddings[original_index] = embedding

                    # 缓存嵌入
                    self._cache_embedding(texts_to_embed[i], embedding)

                # 保存缓存
                if len(texts_to_embed) > 0:
                    self._save_cache()

            except Exception as e:
                logger.error(f"嵌入生成失败: {e}")
                raise

        # 转换为列表格式
        return [embedding.tolist() for embedding in embeddings]

    def embed_query(self, text: str) -> List[float]:
        """
        嵌入查询文本

        Args:
            text: 查询文本

        Returns:
            嵌入向量
        """
        embeddings = self.embed_documents([text])
        return embeddings[0] if embeddings else []

    def similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        计算两个嵌入向量的余弦相似度

        Args:
            embedding1: 嵌入向量1
            embedding2: 嵌入向量2

        Returns:
            相似度分数 (0-1)
        """
        import numpy as np

        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)

        # 计算余弦相似度
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        similarity = dot_product / (norm1 * norm2)
        return float(similarity)

    def get_model_info(self) -> Dict[str, Any]:
        """获取BGE-M3模型信息"""
        if self.model is None:
            return {}

        return {
            "model_name": self.model_name,
            "model_type": "BGE-M3",
            "embedding_dimension": 1024,  # BGE-M3固定维度
            "max_sequence_length": self.max_length,
            "device": self.device,
            "cache_enabled": self.enable_cache,
            "cache_size": len(self.embedding_cache),
            "normalize_embeddings": self.normalize_embeddings,
            "supports_multi_functionality": True,  # 支持密集、稀疏、多向量检索
            "supports_multilingual": True,  # 支持100+语言
        }

    def clear_cache(self):
        """清空缓存"""
        self.embedding_cache.clear()
        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            if cache_file.exists():
                cache_file.unlink()
            logger.info("嵌入缓存已清空")
        except Exception as e:
            logger.warning(f"清空缓存失败: {e}")


# 便捷函数
def create_embeddings(
    model_name: str = None, cache_dir: str = None, device: str = "auto", **kwargs
) -> BGEM3Embeddings:
    """
    创建BGE-M3嵌入服务实例

    Args:
        model_name: 模型名称
        cache_dir: 缓存目录
        device: 设备
        **kwargs: 其他参数

    Returns:
        BGE-M3嵌入服务实例
    """
    return BGEM3Embeddings(
        model_name=model_name, cache_dir=cache_dir, device=device, **kwargs
    )


# 向后兼容性别名
GTELargeEmbeddings = BGEM3Embeddings
