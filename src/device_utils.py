"""
设备工具模块 - 统一的设备选择和管理功能
"""

import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


def get_optimal_device(preferred_device: str = "auto") -> str:
    """
    获取最优的计算设备
    
    Args:
        preferred_device: 首选设备 ("auto", "cpu", "cuda", "mps")
        
    Returns:
        最优设备名称
    """
    if preferred_device != "auto":
        if is_device_available(preferred_device):
            return preferred_device
        else:
            logger.warning(f"首选设备 {preferred_device} 不可用，自动选择最优设备")
    
    # 自动选择最优设备
    try:
        import torch
        
        # 优先级：CUDA > MPS > CPU
        if torch.cuda.is_available():
            device = "cuda"
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            logger.info(f"选择CUDA设备: {gpu_name} (GPU数量: {gpu_count})")
            return device
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = "mps"
            logger.info("选择MPS设备 (Apple Silicon)")
            return device
        else:
            device = "cpu"
            logger.info("选择CPU设备")
            return device
            
    except ImportError:
        logger.warning("PyTorch未安装，默认使用CPU")
        return "cpu"


def is_device_available(device: str) -> bool:
    """
    检查设备是否可用
    
    Args:
        device: 设备名称
        
    Returns:
        设备是否可用
    """
    if device == "cpu":
        return True
    
    try:
        import torch
        
        if device == "cuda":
            return torch.cuda.is_available()
        elif device == "mps":
            return hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()
        else:
            return False
            
    except ImportError:
        return device == "cpu"


def get_device_info(device: str = None) -> Dict[str, Any]:
    """
    获取设备详细信息
    
    Args:
        device: 设备名称，None表示获取当前最优设备信息
        
    Returns:
        设备信息字典
    """
    if device is None:
        device = get_optimal_device()
    
    info = {
        "device": device,
        "available": is_device_available(device),
        "torch_available": False
    }
    
    try:
        import torch
        info["torch_available"] = True
        info["torch_version"] = torch.__version__
        
        if device == "cuda" and torch.cuda.is_available():
            info.update({
                "cuda_version": torch.version.cuda,
                "gpu_count": torch.cuda.device_count(),
                "gpu_names": [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())],
                "memory_info": []
            })
            
            # 获取每个GPU的内存信息
            for i in range(torch.cuda.device_count()):
                memory_info = torch.cuda.get_device_properties(i)
                info["memory_info"].append({
                    "gpu_id": i,
                    "total_memory": memory_info.total_memory,
                    "total_memory_gb": round(memory_info.total_memory / 1024**3, 2)
                })
                
        elif device == "mps" and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            info["mps_available"] = True
            
    except ImportError:
        pass
    
    return info


def recommend_device_settings(model_size_gb: float = 2.3) -> Dict[str, Any]:
    """
    根据模型大小推荐设备设置
    
    Args:
        model_size_gb: 模型大小（GB）
        
    Returns:
        推荐的设备设置
    """
    device = get_optimal_device()
    device_info = get_device_info(device)
    
    recommendations = {
        "device": device,
        "use_fp16": False,
        "batch_size": 32,
        "warnings": []
    }
    
    if device == "cuda" and device_info.get("memory_info"):
        # 获取第一个GPU的内存信息
        gpu_memory_gb = device_info["memory_info"][0]["total_memory_gb"]
        
        if gpu_memory_gb < model_size_gb * 2:
            recommendations["use_fp16"] = True
            recommendations["batch_size"] = 16
            recommendations["warnings"].append(
                f"GPU内存较小 ({gpu_memory_gb}GB)，建议使用半精度和较小批处理大小"
            )
        elif gpu_memory_gb >= model_size_gb * 4:
            recommendations["batch_size"] = 64
            
    elif device == "mps":
        # Apple Silicon 建议使用半精度
        recommendations["use_fp16"] = True
        recommendations["batch_size"] = 16
        
    elif device == "cpu":
        recommendations["batch_size"] = 8
        recommendations["warnings"].append(
            "使用CPU运行，速度较慢，建议考虑使用GPU"
        )
    
    return recommendations


def print_device_summary():
    """打印设备信息摘要"""
    device = get_optimal_device()
    info = get_device_info(device)
    recommendations = recommend_device_settings()
    
    print(f"\n🖥️  设备信息摘要:")
    print(f"   选择设备: {device}")
    print(f"   PyTorch可用: {info['torch_available']}")
    
    if device == "cuda" and info.get("gpu_count", 0) > 0:
        print(f"   GPU数量: {info['gpu_count']}")
        for i, gpu_name in enumerate(info.get("gpu_names", [])):
            memory_gb = info["memory_info"][i]["total_memory_gb"]
            print(f"   GPU {i}: {gpu_name} ({memory_gb}GB)")
    
    print(f"\n⚙️  推荐设置:")
    print(f"   使用半精度: {recommendations['use_fp16']}")
    print(f"   批处理大小: {recommendations['batch_size']}")
    
    if recommendations["warnings"]:
        print(f"\n⚠️  注意事项:")
        for warning in recommendations["warnings"]:
            print(f"   • {warning}")


if __name__ == "__main__":
    print_device_summary()
