"""
CSV风控数据处理器
专门处理CSV格式的单用户风控数据文件，将其分片为向量文档
"""

import logging
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Tuple
from datetime import datetime
import hashlib


from langchain_core.documents import Document

logger = logging.getLogger(__name__)


class CSVRiskProcessor:
    """
    CSV风控数据处理器

    专门处理单用户CSV风控数据，将其转换为向量文档：
    1. 读取CSV文件（单用户风控特征数据）
    2. 将数据分片为向量文档
    3. 生成文件编码用于隔离存储
    """

    def __init__(
        self,
        encoding: str = "utf-8",
        rows_per_chunk: int = 50,  # 每个文档块包含的行数，减少以提高效率
        chunk_overlap_rows: int = 5,  # 分块重叠的行数
    ):
        """
        初始化CSV处理器

        Args:
            encoding: 文件编码
            rows_per_chunk: 每个文档块包含的行数
            chunk_overlap_rows: 分块重叠的行数
        """
        self.encoding = encoding
        self.rows_per_chunk = rows_per_chunk
        self.chunk_overlap_rows = chunk_overlap_rows

        logger.info("CSV风控数据处理器初始化完成")
        logger.info(f"每块行数: {rows_per_chunk}, 重叠行数: {chunk_overlap_rows}")

    def generate_file_code_from_content(self, file_path: Path) -> str:
        """
        基于文件内容生成唯一编码

        Args:
            file_path: 文件路径

        Returns:
            唯一的文件编码
        """
        try:
            # 使用文件内容的哈希值 + 时间戳
            with open(file_path, "rb") as f:
                file_hash = hashlib.md5(f.read()).hexdigest()[:12]

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"csv_{file_hash}_{timestamp}"
        except Exception as e:
            logger.error(f"生成文件编码失败: {e}")
            # 备用方案：使用文件名和时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"csv_{file_path.stem}_{timestamp}"

    def extract_csv_content_as_text(self, df: pd.DataFrame) -> str:
        """
        将DataFrame内容提取为文本格式

        Args:
            df: 数据框

        Returns:
            格式化的文本内容
        """
        content_parts = ["===用户风控数据===", f"数据维度: {len(df)} 行 x {len(df.columns)} 列",
                         f"数据列: {', '.join(df.columns.tolist())}", "", "===详细数据===", "字段名称 | 字段值",
                         "-" * 50]

        # 添加表头

        # 逐行添加数据
        for idx, (_, row) in enumerate(df.iterrows()):
            content_parts.append(f"\n---第{idx + 1}条记录---")
            for col in df.columns:
                value = row[col]
                if pd.isna(value):
                    value = "空值"
                else:
                    value = str(value)
                content_parts.append(f"{col}: {value}")

        # 添加数值列统计
        numeric_cols = df.select_dtypes(include=["number"]).columns
        if len(numeric_cols) > 0:
            content_parts.extend(["\n===数值字段统计==="])
            for col in numeric_cols:
                if not df[col].empty:
                    stats = df[col].describe()
                    content_parts.append(
                        f"{col}: 均值={stats['mean']:.2f}, 最大值={stats['max']}, 最小值={stats['min']}, 标准差={stats['std']:.2f}"
                    )

        return "\n".join(content_parts)

    def create_documents_from_csv(
        self, file_path: Path, file_code: str
    ) -> Tuple[List[Document], Dict[str, Any]]:
        """
        从CSV文件创建文档对象

        Args:
            file_path: CSV文件路径
            file_code: 文件编码

        Returns:
            文档列表和文件信息的元组
        """
        file_path = Path(file_path)
        documents = []

        try:
            # 读取CSV数据
            df = pd.read_csv(file_path, encoding=self.encoding)
            logger.info(f"CSV文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 将CSV内容提取为文本
            csv_content = self.extract_csv_content_as_text(df)

            # 创建主文档
            main_doc = Document(
                page_content=csv_content,
                metadata={
                    "source": str(file_path),
                    "file_name": file_path.name,
                    "file_code": file_code,
                    "data_type": "csv_risk_data",
                    "total_rows": len(df),
                    "total_columns": len(df.columns),
                    "columns": ", ".join(df.columns.tolist()),  # 转换为字符串
                    "created_at": datetime.now().isoformat(),
                    "content_size": len(csv_content),
                }
            )

            documents.append(main_doc)
            logger.info(f"创建主文档，内容长度: {len(csv_content)} 字符")

            # 如果数据量大，进行分块处理
            if len(df) > self.rows_per_chunk:
                chunk_docs = self._create_chunked_documents(df, file_path, file_code)
                documents.extend(chunk_docs)
                logger.info(f"数据量较大，额外创建 {len(chunk_docs)} 个分块文档")

            # 文件信息
            file_info = {
                "file_path": str(file_path),
                "file_name": file_path.name,
                "file_size": file_path.stat().st_size,
                "row_count": len(df),
                "column_count": len(df.columns),
                "processed_at": datetime.now().isoformat(),
            }

            logger.info(f"✅ CSV文件处理完成，创建 {len(documents)} 个文档")
            return documents, file_info

        except Exception as e:
            logger.error(f"处理CSV文件失败: {e}")
            return [], {}

    def process_csv_file(self, file_path: str) -> Tuple[str, List[Document], Dict[str, Any]]:
        """
        完整处理CSV文件

        Args:
            file_path: CSV文件路径

        Returns:
            文件编码、文档列表和文件信息的元组
        """
        file_path = Path(file_path)

        # 验证文件存在
        if not file_path.exists():
            logger.error(f"文件不存在: {file_path}")
            return "", [], {}

        if not file_path.suffix.lower() == '.csv':
            logger.error(f"文件不是CSV格式: {file_path}")
            return "", [], {}

        # 生成文件编码
        file_code = self.generate_file_code_from_content(file_path)

        # 创建文档
        documents, file_info = self.create_documents_from_csv(file_path, file_code)

        logger.info(f"CSV文件处理完成: {file_path.name} -> {file_code}")
        return file_code, documents, file_info

    def _create_chunked_documents(
        self, df: pd.DataFrame, file_path: Path, file_code: str
    ) -> List[Document]:
        """
        创建分块文档

        Args:
            df: DataFrame数据
            file_path: 文件路径
            file_code: 文件编码

        Returns:
            分块文档列表
        """
        documents = []
        total_rows = len(df)

        # 计算需要的块数
        num_chunks = (total_rows + self.rows_per_chunk - 1) // self.rows_per_chunk
        logger.info(f"将 {total_rows} 行数据分为 {num_chunks} 个文档块")

        for chunk_id in range(num_chunks):
            # 计算当前块的行范围
            start_row = chunk_id * self.rows_per_chunk
            end_row = min(start_row + self.rows_per_chunk, total_rows)

            # 添加重叠行（除了第一块）
            if chunk_id > 0:
                overlap_start = max(0, start_row - self.chunk_overlap_rows)
                chunk_df = df.iloc[overlap_start:end_row]
                actual_start = overlap_start
            else:
                chunk_df = df.iloc[start_row:end_row]
                actual_start = start_row

            # 生成块内容
            chunk_content = self._format_chunk_content(
                chunk_df, chunk_id, actual_start, end_row - 1, total_rows
            )

            # 创建文档
            doc = Document(
                page_content=chunk_content,
                metadata={
                    "source": str(file_path),
                    "file_name": file_path.name,
                    "file_code": file_code,
                    "chunk_id": chunk_id,
                    "chunk_start_row": actual_start,
                    "chunk_end_row": end_row - 1,
                    "total_chunks": num_chunks,
                    "total_rows": total_rows,
                    "data_type": "csv_risk_data",
                    "created_at": datetime.now().isoformat(),
                    "chunk_size": len(chunk_content),
                    "rows_in_chunk": len(chunk_df),
                }
            )

            documents.append(doc)
            logger.debug(
                f"创建文档块 {chunk_id + 1}/{num_chunks}，行范围: {actual_start}-{end_row-1}"
            )

        return documents

    def _format_chunk_content(
        self,
        chunk_df: pd.DataFrame,
        chunk_id: int,
        start_row: int,
        end_row: int,
        total_rows: int,
    ) -> str:
        """
        格式化块内容

        Args:
            chunk_df: 块数据
            chunk_id: 块ID
            start_row: 起始行
            end_row: 结束行
            total_rows: 总行数

        Returns:
            格式化的内容字符串
        """
        content_parts = [
            f"===风控数据块{chunk_id + 1}===",
            f"数据范围:第{start_row + 1}行-第{end_row + 1}行(共{total_rows}行)",
            f"本块行数:{len(chunk_df)}",
            f"数据列:{', '.join(chunk_df.columns.tolist())}",
            "",
            "===数据内容===",
        ]

        # 添加表头
        content_parts.append("列名:" + " | ".join(chunk_df.columns.tolist()))
        content_parts.append("-" * 50)

        # 添加数据行
        for idx, (_, row) in enumerate(chunk_df.iterrows()):
            row_data = []
            for col in chunk_df.columns:
                value = row[col]
                # 处理空值
                if pd.isna(value):
                    value = "空值"
                else:
                    value = str(value)
                row_data.append(value)

            content_parts.append(f"第{start_row + idx + 1}行: " + " | ".join(row_data))

        # 添加统计信息
        content_parts.extend(["", "===数据统计===", "数值列统计:"])

        # 添加数值列的基本统计
        numeric_cols = chunk_df.select_dtypes(include=["number"]).columns
        for col in numeric_cols:
            if not chunk_df[col].empty:
                stats = chunk_df[col].describe()
                content_parts.append(
                    f"{col}: 均值={stats['mean']:.2f}, 最大值={stats['max']}, 最小值={stats['min']}"
                )

        return "\n".join(content_parts)
