"""
风险预测系统主程序
基于文件编码的个人信贷风险预测系统
"""

import os
import sys
import argparse
from pathlib import Path
from typing import Optional
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.risk_prediction_api import RiskPredictionAPI
    from src.file_code_manager import FileCodeManager
    from config import settings
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class RiskPredictionMain:
    """风险预测系统主程序"""

    def __init__(self, api_key: Optional[str] = None):
        """
        初始化主程序

        Args:
            api_key: DeepSeek API 密钥
        """
        # self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        self.api_key = "***********************************"
        if not self.api_key:
            logger.error("未设置 DeepSeek API 密钥，请设置环境变量 DEEPSEEK_API_KEY")
            sys.exit(1)

        # 初始化组件
        self.risk_api = RiskPredictionAPI(api_key=self.api_key)
        self.file_manager = FileCodeManager()

        logger.info("✅ 风险预测系统初始化完成")

    def upload_file(self, file_path: str) -> str:
        """
        上传csv文件并返回文件编码

        Args:
            file_path: Excel文件路径

        Returns:
            文件编码
        """
        print(f"\n📁 正在上传文件: {file_path}")

        result = self.risk_api.upload_csv_and_get_code(file_path)

        if result["success"]:
            file_code = result["file_code"]
            print(f"✅ 文件上传成功")
            print(f"   文件编码: {file_code}")
            print(
                f"   文件名: {result.get('file_info', {}).get('file_name', 'unknown')}"
            )
            print(f"   文档数量: {result['document_count']}")
            return file_code
        else:
            print(f"❌ 文件上传失败: {result['error']}")
            return ""

    def predict_risk(self, file_code: str) -> None:
        """
        根据文件编码进行风险预测

        Args:
            file_code: 文件编码
        """
        print(f"\n🔍 正在分析文件编码: {file_code}")

        # 检查文件编码是否存在
        if not self.file_manager.check_file_code_exists(file_code):
            print(f"❌ 文件编码 {file_code} 不存在")
            return

        # 进行风险预测
        result = self.risk_api.predict_by_file_code(file_code)

        if result["success"]:
            print(f"✅ 风险分析完成")
            print(f"\n" + "=" * 60)
            print(f"📊 风险预测报告")
            print(f"=" * 60)
            print(f"文件编码: {result['file_code']}")
            print(f"分析时间: {result['analysis_timestamp']}")
            print(f"违约概率: {result['default_probability']:.2%}")
            print(f"风险等级: {result['risk_level']}")
            print(f"风险评分: {result['risk_score']:.4f}")
            print(f"数据来源: {result['document_count']} 个文档")
            print(f"\n📝 AI分析结果:")
            print("-" * 40)
            print(result["analysis_summary"])
            print("=" * 60)
        else:
            print(f"❌ 风险分析失败: {result['error']}")

    def list_files(self) -> None:
        """列出所有可用的文件"""
        print(f"\n📋 可用文件列表:")

        files = self.file_manager.list_available_codes()

        if not files:
            print("   暂无可用文件")
            return

        print(f"   共找到 {len(files)} 个文件:")
        print(f"   {'序号':<4} {'文件编码':<25} {'文件名':<20} {'上传时间':<20}")
        print(f"   {'-'*4} {'-'*25} {'-'*20} {'-'*20}")

        for i, file_info in enumerate(files, 1):
            file_code = file_info["file_code"]
            file_name = file_info.get("file_name", "unknown")[:18]
            created_at = file_info.get("created_at", "unknown")[:19]
            print(f"   {i:<4} {file_code:<25} {file_name:<20} {created_at:<20}")

    def get_statistics(self) -> None:
        """获取系统统计信息"""
        print(f"\n📈 系统统计信息:")

        stats = self.file_manager.get_statistics()

        print(f"   总文件数: {stats['total_files']}")
        print(f"   总文档数: {stats['total_documents']}")
        print(f"   总文件大小: {stats['total_file_size'] / 1024 / 1024:.2f} MB")

        if stats.get("recent_files"):
            print(f"\n   最近上传的文件:")
            for file_info in stats["recent_files"][:3]:
                print(
                    f"   - {file_info.get('file_name', 'unknown')} ({file_info['file_code']})"
                )

    def run_interactive(self) -> None:
        """运行交互式模式"""
        print("\n🎯 风险预测系统 - 交互式模式")
        print("输入 'help' 查看帮助信息")

        while True:
            try:
                command = input("\n> ").strip()

                if command.lower() in ["exit", "quit", "q"]:
                    print("👋 再见！")
                    break
                elif command.lower() == "help":
                    self.show_help()
                elif command.lower() == "list":
                    self.list_files()
                elif command.lower() == "stats":
                    self.get_statistics()
                elif command.startswith("upload "):
                    file_path = command[7:].strip()
                    self.upload_file(file_path)
                elif command.startswith("predict "):
                    file_code = command[8:].strip()
                    self.predict_risk(file_code)
                else:
                    print("❓ 未知命令，输入 'help' 查看帮助")

            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 执行命令时出错: {e}")

    def show_help(self) -> None:
        """显示帮助信息"""
        print(f"\n📖 帮助信息:")
        print(f"   upload <文件路径>     - 上传Excel文件")
        print(f"   predict <文件编码>    - 根据文件编码进行风险预测")
        print(f"   list                 - 列出所有可用文件")
        print(f"   stats                - 显示系统统计信息")
        print(f"   help                 - 显示此帮助信息")
        print(f"   exit/quit/q          - 退出程序")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="基于文件编码的个人信贷风险预测系统")
    parser.add_argument("--upload", "-u", help="上传CSV文件路径")
    parser.add_argument("--predict", "-p", help="根据文件编码进行风险预测")
    parser.add_argument("--list", "-l", action="store_true", help="列出所有可用文件")
    parser.add_argument("--stats", "-s", action="store_true", help="显示系统统计信息")
    parser.add_argument(
        "--interactive", "-i", action="store_true", help="运行交互式模式"
    )
    parser.add_argument("--api-key", help="DeepSeek API 密钥")

    args = parser.parse_args()

    try:
        # 初始化系统
        system = RiskPredictionMain(api_key=args.api_key)

        # 根据参数执行相应操作
        if args.upload:
            file_code = system.upload_file(args.upload)
            if file_code and input("\n是否立即进行风险预测？(y/n): ").lower() == "y":
                system.predict_risk(file_code)
        elif args.predict:
            system.predict_risk(args.predict)
        elif args.list:
            system.list_files()
        elif args.stats:
            system.get_statistics()
        elif args.interactive:
            system.run_interactive()
        else:
            # 默认显示帮助并进入交互模式
            print("🎯 欢迎使用风险预测系统！")
            system.show_help()

            if input("\n是否进入交互式模式？(y/n): ").lower() == "y":
                system.run_interactive()

    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
