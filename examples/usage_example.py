#!/usr/bin/env python3
"""
Deep Risk RAG 系统使用示例
演示如何使用各个组件
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.document_loader import DocumentProcessor
from src.embeddings import BGEM3Embeddings
from src.vector_store import ChromaVectorStore, VectorStoreManager
from src.rag_chain import DeepSeekRAGChain
from langchain_core.documents import Document


def example_1_document_processing():
    """示例1: 文档处理"""
    print("=" * 50)
    print("示例1: 文档处理")
    print("=" * 50)

    # 创建文档处理器
    processor = DocumentProcessor(chunk_size=500, chunk_overlap=100)

    # 创建示例文档
    sample_text = """
    人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
    它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
    该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
    
    机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习。
    深度学习是机器学习的一个子集，它基于人工神经网络，特别是深层神经网络。
    
    自然语言处理（NLP）是人工智能和语言学领域的分支学科，
    它研究能实现人与计算机之间用自然语言进行有效通信的各种理论和方法。
    """

    # 创建文档对象
    doc = Document(
        page_content=sample_text,
        metadata={"source": "ai_introduction.txt", "file_name": "ai_introduction.txt"},
    )

    # 分割文档
    split_docs = processor.split_documents([doc])

    print(f"原始文档长度: {len(sample_text)} 字符")
    print(f"分割后文档数量: {len(split_docs)}")

    for i, chunk in enumerate(split_docs):
        print(f"\n文档块 {i+1}:")
        print(f"长度: {len(chunk.page_content)} 字符")
        print(f"内容预览: {chunk.page_content[:100]}...")
        print(f"元数据: {chunk.metadata}")


def example_2_embeddings():
    """示例2: 文本嵌入"""
    print("\n" + "=" * 50)
    print("示例2: 文本嵌入")
    print("=" * 50)

    try:
        # 创建嵌入服务
        embeddings = BGEM3Embeddings(enable_cache=False)

        # 测试文本
        texts = [
            "人工智能是计算机科学的一个分支",
            "机器学习是AI的重要组成部分",
            "深度学习基于神经网络",
            "自然语言处理研究人机交互",
        ]

        print("正在生成嵌入...")

        # 生成嵌入
        embeddings_result = embeddings.embed_documents(texts)

        print(f"文本数量: {len(texts)}")
        print(f"嵌入维度: {len(embeddings_result[0])}")

        # 计算相似度
        sim1 = embeddings.similarity(embeddings_result[0], embeddings_result[1])
        sim2 = embeddings.similarity(embeddings_result[0], embeddings_result[3])

        print(f"\n相似度测试:")
        print(f"'{texts[0]}' 与 '{texts[1]}' 的相似度: {sim1:.3f}")
        print(f"'{texts[0]}' 与 '{texts[3]}' 的相似度: {sim2:.3f}")

        # 显示模型信息
        model_info = embeddings.get_model_info()
        print(f"\n模型信息: {model_info}")

    except Exception as e:
        print(f"嵌入示例失败: {e}")
        print("请确保已下载模型: python models/download_models.py")


def example_3_vector_store():
    """示例3: 向量存储"""
    print("\n" + "=" * 50)
    print("示例3: 向量存储")
    print("=" * 50)

    try:
        # 创建嵌入服务
        embeddings = BGEM3Embeddings(enable_cache=False)

        # 创建向量存储
        vector_store = ChromaVectorStore(
            embeddings=embeddings,
            persist_directory="./cache/example_chroma",
            collection_name="example_collection",
        )

        # 创建示例文档
        docs = [
            Document(
                page_content="人工智能是模拟人类智能的技术",
                metadata={"source": "ai_doc1.txt", "file_name": "ai_doc1.txt"},
            ),
            Document(
                page_content="机器学习让计算机能够自动学习",
                metadata={"source": "ml_doc1.txt", "file_name": "ml_doc1.txt"},
            ),
            Document(
                page_content="深度学习使用多层神经网络",
                metadata={"source": "dl_doc1.txt", "file_name": "dl_doc1.txt"},
            ),
            Document(
                page_content="自然语言处理处理人类语言",
                metadata={"source": "nlp_doc1.txt", "file_name": "nlp_doc1.txt"},
            ),
        ]

        # 添加文档
        print("正在添加文档到向量存储...")
        doc_ids = vector_store.add_documents(docs)
        print(f"成功添加 {len(doc_ids)} 个文档")

        # 搜索测试
        query = "什么是人工智能？"
        print(f"\n搜索查询: '{query}'")

        results = vector_store.similarity_search(query, k=2)

        print(f"找到 {len(results)} 个相关文档:")
        for i, doc in enumerate(results, 1):
            print(f"  {i}. {doc.metadata['file_name']}: {doc.page_content}")

        # 带分数的搜索
        results_with_score = vector_store.similarity_search_with_score(query, k=2)

        print(f"\n带分数的搜索结果:")
        for doc, score in results_with_score:
            print(
                f"  分数: {score:.3f} - {doc.metadata['file_name']}: {doc.page_content}"
            )

        # 显示集合信息
        info = vector_store.get_collection_info()
        print(f"\n向量存储信息: {info}")

    except Exception as e:
        print(f"向量存储示例失败: {e}")


def example_4_rag_chain():
    """示例4: RAG 问答链"""
    print("\n" + "=" * 50)
    print("示例4: RAG 问答链")
    print("=" * 50)

    # 检查 API 密钥
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 请设置 DEEPSEEK_API_KEY 环境变量")
        print("示例: export DEEPSEEK_API_KEY='your_api_key_here'")
        return

    try:
        # 创建嵌入服务
        embeddings = BGEM3Embeddings(enable_cache=False)

        # 创建向量存储
        vector_store = ChromaVectorStore(
            embeddings=embeddings,
            persist_directory="./cache/example_chroma",
            collection_name="rag_example_collection",
        )

        # 添加一些示例文档
        docs = [
            Document(
                page_content="""
                人工智能（AI）的发展历程可以分为几个重要阶段：
                1. 1950年代：图灵测试的提出，标志着AI概念的诞生
                2. 1960-1970年代：专家系统的兴起
                3. 1980年代：机器学习算法的发展
                4. 2000年代：大数据和深度学习的突破
                5. 2010年代至今：深度学习在各领域的广泛应用
                """,
                metadata={"source": "ai_history.txt", "file_name": "AI发展史.txt"},
            ),
            Document(
                page_content="""
                机器学习的主要类型包括：
                1. 监督学习：使用标记数据训练模型，如分类和回归
                2. 无监督学习：从无标记数据中发现模式，如聚类
                3. 强化学习：通过与环境交互学习最优策略
                4. 半监督学习：结合少量标记数据和大量无标记数据
                5. 迁移学习：将已学知识应用到新任务
                """,
                metadata={"source": "ml_types.txt", "file_name": "机器学习类型.txt"},
            ),
            Document(
                page_content="""
                深度学习的核心技术包括：
                1. 卷积神经网络（CNN）：主要用于图像处理
                2. 循环神经网络（RNN）：适合处理序列数据
                3. 长短期记忆网络（LSTM）：解决RNN的长期依赖问题
                4. Transformer：革命性的注意力机制架构
                5. 生成对抗网络（GAN）：用于生成新数据
                """,
                metadata={"source": "dl_tech.txt", "file_name": "深度学习技术.txt"},
            ),
        ]

        # 清空并添加文档
        vector_store.clear_collection()
        doc_ids = vector_store.add_documents(docs)
        print(f"成功添加 {len(doc_ids)} 个文档到知识库")

        # 创建 RAG 链
        rag_chain = DeepSeekRAGChain(vector_store=vector_store, api_key=api_key)

        # 测试问题
        questions = [
            "人工智能的发展经历了哪些重要阶段？",
            "机器学习有哪些主要类型？",
            "深度学习包含哪些核心技术？",
            "什么是Transformer？",
        ]

        print("\n开始问答测试:")
        for i, question in enumerate(questions, 1):
            print(f"\n问题 {i}: {question}")
            print("-" * 40)

            result = rag_chain.ask(question, include_sources=True)

            if result["success"]:
                print(f"回答: {result['answer']}")

                sources = result.get("sources", [])
                if sources:
                    print(f"\n参考来源:")
                    for j, source in enumerate(sources[:2], 1):
                        print(f"  {j}. {source['file_name']}")
            else:
                print(f"❌ 回答失败: {result.get('answer', '未知错误')}")

        # 显示链信息
        chain_info = rag_chain.get_chain_info()
        print(f"\nRAG 链信息: {chain_info}")

    except Exception as e:
        print(f"RAG 链示例失败: {e}")


def main():
    """运行所有示例"""
    print("🤖 Deep Risk RAG 系统使用示例")
    print("=" * 60)

    # 运行示例
    example_1_document_processing()
    example_2_embeddings()
    example_3_vector_store()
    example_4_rag_chain()

    print("\n" + "=" * 60)
    print("✅ 所有示例运行完成！")
    print("\n要开始使用系统，请运行: python main.py")


if __name__ == "__main__":
    main()
