"""
配置文件 - Deep Risk RAG 系统
"""

from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """系统配置类"""

    # 项目路径
    PROJECT_ROOT: Path = Path(__file__).parent
    DATA_DIR: Path = PROJECT_ROOT / "data"
    DOCUMENTS_DIR: Path = DATA_DIR / "documents"
    CACHE_DIR: Path = PROJECT_ROOT / "cache"
    MODELS_CACHE_DIR: Path = CACHE_DIR / "models"

    # DeepSeek API 配置
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
    DEEPSEEK_MODEL: str = "deepseek-reasoner"

    # 嵌入模型配置
    EMBEDDING_MODEL_NAME: str = "thenlper/gte-large"
    EMBEDDING_MODEL_CACHE_DIR: Optional[str] = None
    EMBEDDING_BATCH_SIZE: int = 32
    EMBEDDING_MAX_LENGTH: int = 512  # GTE-Large 模型的硬限制
    
    # 文本处理策略配置
    TEXT_PROCESSING_STRATEGY: str = "smart_truncate"  # smart_truncate, head_tail, sliding_window
    ENABLE_TEXT_SPLITTING: bool = True  # 是否将长文本分割为多个嵌入
    TEXT_SPLIT_OVERLAP: int = 50  # 文本分割时的重叠字符数
    MAX_TEXT_CHARS_PER_EMBEDDING: int = 2048  # 每个嵌入的最大字符数 (512 tokens * 4)

    # 向量数据库配置
    VECTOR_DB_TYPE: str = "chroma"  # chroma, faiss
    CHROMA_PERSIST_DIR: str = "./cache/chroma_db"
    CHROMA_COLLECTION_NAME: str = "deep_risk_documents"

    # 文档处理配置
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    MAX_DOCUMENT_SIZE_MB: int = 50

    # 检索配置
    RETRIEVAL_TOP_K: int = 5
    SIMILARITY_THRESHOLD: float = 0.7

    # RAG 配置
    ENABLE_DOCUMENT_RETRIEVAL: bool = True  # 是否启用文档检索（关闭后变为纯LLM对话）
    MAX_CONTEXT_LENGTH: int = 32000  # 最大上下文长度（DeepSeek支持64K+，32K约8K tokens）
    TEMPERATURE: float = 0.1
    MAX_TOKENS: int = 4000  # DeepSeek API 限制范围 [1, 8192]，4000 在合理范围内

    # 风险预测配置
    RISK_PREDICTION_ENABLED: bool = True  # 是否启用风险预测功能
    FILE_CODED_VECTOR_STORE_DIR: str = "./cache/file_coded_chroma"  # 文件编码向量存储目录
    RISK_ANALYSIS_TIMEOUT: int = 300  # 风险分析超时时间（秒）
    MAX_EXCEL_FILE_SIZE_MB: int = 10  # Excel文件最大大小限制（MB）

    # 系统配置
    LOG_LEVEL: str = "INFO"
    ENABLE_CACHE: bool = True
    CACHE_TTL_SECONDS: int = 3600

    # ChromaDB 配置
    ANONYMIZED_TELEMETRY: bool = False

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 全局配置实例
settings = Settings()


# 确保必要的目录存在
def ensure_directories():
    """确保所有必要的目录存在"""
    directories = [
        settings.DATA_DIR,
        settings.DOCUMENTS_DIR,
        settings.CACHE_DIR,
        settings.MODELS_CACHE_DIR,
        Path(settings.CHROMA_PERSIST_DIR).parent,
    ]

    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

    print(f"✅ 项目目录结构已创建")
    print(f"📁 数据目录: {settings.DOCUMENTS_DIR}")
    print(f"📁 缓存目录: {settings.CACHE_DIR}")
    print(f"📁 模型缓存: {settings.MODELS_CACHE_DIR}")


if __name__ == "__main__":
    ensure_directories()
